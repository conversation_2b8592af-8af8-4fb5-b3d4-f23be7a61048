import { IMessageRepository, CreateMessageParams, UpdateMessageParams } from '../MessageRepository';
import { Msg, MessageStatus } from '@/core/types/chat';
import { messageService } from '@/services_to_migrate_ddd/messageService';
import { messageDb } from '../persistence/MessageDatabase';

export class MessageRepositoryImpl implements IMessageRepository {
  async create(params: CreateMessageParams): Promise<Msg> {
    // 使用现有的 messageService 创建消息
    const msg = await messageService.addMessage(
      params.chatId,
      params.content,
      params.role,
      {
        short: params.short,
        model: params.model,
        timestamp: params.timestamp,
        status: params.status || MessageStatus.IDLE,
      }
    );

    return msg;
  }

  async update(params: UpdateMessageParams): Promise<void> {
    // 获取现有消息
    const existingMsg = await this.findById(params.messageId);
    if (!existingMsg) {
      throw new Error(`Message with id ${params.messageId} not found`);
    }

    // 构建更新的消息对象
    const updatedMsg: Msg = {
      ...existingMsg,
      ...(params.content !== undefined && { content: params.content }),
      ...(params.thinkContent !== undefined && { thinkContent: params.thinkContent }),
      ...(params.status !== undefined && { status: params.status }),
      ...(params.short !== undefined && { short: params.short }),
    };

    // 使用 messageDb 更新消息
    await messageDb.updateMessage(params.messageId, updatedMsg);
  }

  async findById(messageId: string): Promise<Msg | undefined> {
    try {
      const messageRecord = await messageDb.getMessage(messageId);
      if (!messageRecord) {
        return undefined;
      }

      // 转换 MessageRecord 到 Msg
      const msg: Msg = {
        id: messageRecord.id,
        content: messageRecord.content,
        short: messageRecord.short,
        role: messageRecord.role,
        status: messageRecord.status,
        model: messageRecord.model,
        timestamp: messageRecord.timestamp,
        thinkContent: messageRecord.thinkContent,
      };

      return msg;
    } catch (error) {
      console.error('Error finding message by id:', error);
      return undefined;
    }
  }

  async findByChatId(chatId: string): Promise<Msg[]> {
    try {
      // 使用现有的 messageService 获取聊天消息
      const messages = await messageService.getChatMessages(chatId);
      return messages;
    } catch (error) {
      console.error('Error finding messages by chat id:', error);
      return [];
    }
  }

  async delete(messageId: string): Promise<void> {
    try {
      await messageDb.deleteMessage(messageId);
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }
}
