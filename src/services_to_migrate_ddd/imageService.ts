import type { ImageRecord } from "@/core/types/chat.ts";
import { imageDb } from "@/infrastructure/persistence/ImageDatabase";

/**
 * 图片服务 - 负责处理图片相关的业务逻辑，如创建、存储和检索。
 */
export class ImageService {
    /**
     * 从文件对象创建一个可用于存储的图片记录。
     * @param file 用户选择的图片文件
     * @returns 一个准备好存入数据库的图片记录对象 (除了id和创建时间)
     */
    async createImageRecordFromFile(
        file: File
    ): Promise<Omit<ImageRecord, "id" | "createdAt">> {
        return {
            name: file.name,
            blob: new Blob([file], { type: file.type }),
            mimeType: file.type,
            size: file.size,
        };
    }

    /**
     * 将图片文件添加到数据库并返回其ID。
     * @param file 图片文件
     * @returns 存入数据库后图片的唯一ID
     */
    async addImage(file: File): Promise<number> {
        const imageRecordData = await this.createImageRecordFromFile(file);
        return await imageDb.addImage(imageRecordData);
    }

    /**
     * 根据ID从数据库中删除一张图片。
     * @param imageId 要删除的图片ID
     */
    async deleteImage(imageId: number): Promise<void> {
        await imageDb.deleteImage(imageId);
    }

    /**
     * 根据ID获取图片的Blob URL用于预览。
     * @param imageId 图片ID
     * @returns 一个可用于 `<img>` src 属性的 URL，如果找不到图片则返回 null
     */
    async getBlobUrl(imageId: number): Promise<string | null> {
        return await imageDb.getBlobUrl(imageId);
    }

    /**
     * 释放一个之前通过 getBlobUrl 创建的 Blob URL，以释放内存。
     * @param url 要释放的 Blob URL
     */
    releaseBlobUrl(url: string): void {
        imageDb.releaseBlobUrl(url);
    }
}

// 导出单例实例
export const imageService = new ImageService();
