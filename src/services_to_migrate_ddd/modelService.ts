import type { ProviderConfig } from "@/core/types/model.ts";
import OpenAI from "openai";
import { resolvedEnvFetch } from "@/core/utils/fetch.ts";

/**
 * 模型服务 - 负责处理与模型列表获取和管理相关的业务逻辑
 */
export class ModelService {
    /**
     * 从指定供应商的API获取模型列表
     * @param providerConfig 要获取模型的供应商配置
     * @returns 一个包含模型ID字符串的数组
     * @throws 如果API调用失败或配置不正确，则会抛出错误
     */
    async fetchModelsFromApi(
        providerConfig: ProviderConfig
    ): Promise<string[]> {
        console.log(
            `[ModelService] 开始获取 ${
                providerConfig.displayName || providerConfig.key
            } 的模型列表...`
        );

        try {
            // 直接创建 OpenAI 实例
            const openai = new OpenAI({
                apiKey: providerConfig.apiKey,
                baseURL: providerConfig.baseUrl,
                dangerouslyAllowBrowser: true,
                fetch: resolvedEnvFetch as any,
            });

            // 调用API
            const response = await openai.models.list();
            const apiModelList = response.data.map((model: any) => model.id);

            console.log(
                `[ModelService] 成功获取到 ${apiModelList.length} 个模型。`
            );

            return apiModelList;
        } catch (error) {
            const displayName =
                providerConfig.displayName || providerConfig.key;
            const errorMessage = `获取 ${displayName} 模型列表失败，请检查API配置。`;
            console.error(`${errorMessage}:`, error);
            // 重新抛出错误，以便上层（如Store的Action）可以捕获并处理
            throw new Error(errorMessage);
        }
    }
}

export const modelService = new ModelService();
