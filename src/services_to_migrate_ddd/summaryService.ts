import type { OpenAI } from "openai";

/**
 * 摘要服务 - 负责纯API调用，不包含业务逻辑
 * 
 * 注意：业务逻辑已迁移到 SummaryUseCase
 * 此服务现在只负责底层的API调用
 */
export class SummaryService {
    /**
     * 调用 OpenAI API 生成摘要 (纯API调用)
     * @param openai - OpenAI 实例
     * @param model - 使用的模型
     * @param prompt - 完整的提示词
     * @returns API 响应的原始内容
     * @throws 如果 API 调用失败，则会抛出错误
     */
    async callSummaryAPI(
        openai: OpenAI,
        model: string,
        prompt: string
    ): Promise<string> {
        const completion = await openai.chat.completions.create({
            model: model,
            messages: [
                {
                    role: "user",
                    content: prompt,
                },
            ],
        });

        return completion.choices[0].message.content || "";
    }
}

// 导出单例实例
export const summaryService = new SummaryService();
