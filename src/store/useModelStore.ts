import { useStorage } from "@vueuse/core";
import { computed } from "vue";
import { defineStore } from "pinia";
import OpenAI from "openai";
import type { ProviderConfig, ProviderCreationData } from "@/core/types/model.ts";
import { modelService } from "@/services_to_migrate_ddd/modelService.ts";
import { validatorService } from "@/services_to_migrate_ddd/validatorService.ts";
import { providerConfigs as presetProviderConfigs } from "@/core/config/provider.ts";
import { mergeProviders, createProvider } from "@/core/types/provider.ts";
import { resolvedEnvFetch } from "@/core/utils/fetch.ts";

export const useModelStore = defineStore("model", () => {
    // --- State ---
    const providerConfigs = useStorage<Array<ProviderConfig>>(
        "config/providerList",
        []
    );
    const model = useStorage<string>("config/defaultModel", "");
    const provider = useStorage<string>("config/defaultModelProvider", "");

    // --- Computed Properties (Getters) ---

    const providerList = computed<ProviderConfig[]>(() => {
        return providerConfigs.value;
    });

    const activeProviders = computed(() =>
        providerList.value.filter((p) => p.isActive)
    );

    const allModelList = computed(() => {
        return [
            ...new Set(
                activeProviders.value.flatMap((p) => p.selectedModels || [])
            ),
        ];
    });

    const hasModels = computed(() => allModelList.value.length > 0);

    const getProvider = (providerId: string): ProviderConfig | undefined => {
        return providerList.value.find((p) => p.key === providerId);
    };

    // --- Actions ---

    // Provider Actions
    function addProvider(providerData: ProviderCreationData) {
        const newProvider = createProvider(providerData);
        providerConfigs.value.push(newProvider);
        return newProvider.key;
    }

    function updateProvider(
        providerId: string,
        updatedData: Partial<ProviderConfig>
    ) {
        const configs = providerConfigs.value;
        const index = configs.findIndex((p) => p.key === providerId);

        if (index !== -1) {
            const updatedProvider = { ...configs[index], ...updatedData };
            configs.splice(index, 1, updatedProvider);
        }

        // 如果激活状态发生变化，处理默认供应商和模型的自动设置
        if (updatedData.isActive !== undefined) {
            const updatedProvider = getProvider(providerId);
            if (updatedProvider && updatedData.isActive) {
                // 供应商被激活时，如果当前没有默认供应商，设置为默认
                if (!provider.value) {
                    provider.value = providerId;
                }

                // 如果当前没有选择模型，自动选择该供应商的第一个模型
                if (!model.value) {
                    const availableModels =
                        updatedProvider.selectedModels || [];
                    if (availableModels.length > 0) {
                        model.value = availableModels[0];
                    }
                }
            }
        }

        // 如果用户选择的模型列表发生变化，处理当前模型的自动设置
        if (updatedData.selectedModels !== undefined) {
            const updatedProvider = getProvider(providerId);
            if (updatedProvider && providerId === provider.value) {
                const newSelectedModels = updatedData.selectedModels;

                // 如果当前没有选择模型，自动选择第一个新添加的模型
                if (!model.value && newSelectedModels.length > 0) {
                    model.value = newSelectedModels[0];
                }

                // 如果当前选择的模型被移除了，切换到其他可用模型
                if (model.value && !newSelectedModels.includes(model.value)) {
                    if (newSelectedModels.length > 0) {
                        model.value = newSelectedModels[0];
                    } else {
                        model.value = "";
                    }
                }
            }
        }
    }

    function removeProvider(providerId: string) {
        const configs = providerConfigs.value;
        const index = configs.findIndex((p) => p.key === providerId);

        if (index !== -1) {
            // 安全检查：只允许删除自定义源的供应商
            if (configs[index].origin !== "custom") {
                console.warn(
                    `Attempted to delete a preset provider ("${providerId}"). This is not allowed.`
                );
                return;
            }
            configs.splice(index, 1);
        }
    }

    function setProviderActive(providerId: string, isActive: boolean) {
        updateProvider(providerId, { isActive });
    }

    // Model Actions
    async function fetchProviderModels(providerId: string) {
        const providerConfig = getProvider(providerId);
        if (!providerConfig) {
            throw new Error(`Provider ${providerId} not found.`);
        }

        try {
            const models = await modelService.fetchModelsFromApi(
                providerConfig
            );
            updateProvider(providerId, {
                providerModels: models,
            });

            // Auto-switch logic if current model becomes invalid
            if (
                providerId === provider.value &&
                model.value &&
                !models.includes(model.value)
            ) {
                const selectedModels = providerConfig.selectedModels || [];
                const availableSelected = selectedModels.find((m) =>
                    models.includes(m)
                );
                if (availableSelected) {
                    model.value = availableSelected;
                } else if (models.length > 0) {
                    model.value = models[0];
                } else {
                    model.value = "";
                }
            }

            return models;
        } catch (error) {
            const errorMessage =
                error instanceof Error ? error.message : "Unknown error";
            throw error; // Re-throw for UI to handle
        }
    }

    function addModelToProvider(providerId: string, modelName: string) {
        const provider = getProvider(providerId);
        if (provider) {
            const currentModels = provider.selectedModels || [];
            if (!currentModels.includes(modelName)) {
                updateProvider(providerId, {
                    selectedModels: [...currentModels, modelName],
                });
            }
        }
    }

    function removeModelFromProvider(providerId: string, modelName: string) {
        const provider = getProvider(providerId);
        if (provider) {
            const updatedModels = (provider.selectedModels || []).filter(
                (m) => m !== modelName
            );
            updateProvider(providerId, { selectedModels: updatedModels });
        }
    }

    function deleteModelFromAvailable(providerId: string, modelName: string) {
        const provider = getProvider(providerId);
        if (provider) {
            const providerModels = provider.providerModels || [];
            if (providerModels.length > 0) {
                const updatedProviderModels = providerModels.filter(
                    (m) => m !== modelName
                );
                updateProvider(providerId, {
                    providerModels: updatedProviderModels,
                });
            }

            const configModels = provider.selectedModels || [];
            if (configModels.includes(modelName)) {
                const updatedModels = configModels.filter(
                    (m) => m !== modelName
                );
                updateProvider(providerId, { selectedModels: updatedModels });
            }
        }
    }

    // Instance Creation
    async function createOpenAIInstance(
        specifiedProviderId?: string
    ): Promise<OpenAI | null> {
        const providerToUseId = specifiedProviderId || provider.value;
        if (!providerToUseId) return null;

        const providerConfig = getProvider(providerToUseId);
        if (!providerConfig) return null;

        try {
            if (!providerConfig.apiKey || !providerConfig.baseUrl) {
                throw new Error(
                    `Provider "${providerConfig.displayName}" is missing API Key or Base URL.`
                );
            }
            return new OpenAI({
                apiKey: providerConfig.apiKey,
                baseURL: providerConfig.baseUrl,
                dangerouslyAllowBrowser: true,
                fetch: resolvedEnvFetch as any,
            });
        } catch (error) {
            console.error("Failed to create OpenAI instance:", error);
            return null;
        }
    }

    // --- URL Parameter Handling & Initialization ---
    function checkUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const apiKey = urlParams.get("apiKey");
        const baseUrl = urlParams.get("baseUrl");
        const modelParam = urlParams.get("model");

        // Assuming 'custom' is the key for a generic custom provider
        const customProvider = providerList.value.find(
            (p) => p.key === "custom"
        );
        if (customProvider) {
            const updates: Partial<ProviderConfig> = {};
            if (apiKey) updates.apiKey = apiKey;
            if (baseUrl) {
                try {
                    new URL(baseUrl); // Validate URL
                    updates.baseUrl = baseUrl;
                } catch (e) {
                    console.error("URL参数中提供的Base URL无效:", e);
                }
            }
            if (Object.keys(updates).length > 0) {
                updateProvider(customProvider.key, updates);
            }
        }

        if (modelParam) model.value = modelParam;
    }

    function initialize() {
        providerConfigs.value = mergeProviders(
            presetProviderConfigs,
            providerConfigs.value
        );
        checkUrlParameters();
    }

    // --- Validator ---
    /**
     * 校验当前 provider 的 API 配置是否有效
     */
    function isApiConfigured(): boolean {
        const p = getProvider(provider.value);
        return validatorService.isApiConfigured(p);
    }

    /**
     * 校验当前模型配置是否有效，返回详细原因
     */
    function isModelConfigured() {
        const p = getProvider(provider.value);
        // 修复拼写错误: isModelConfigred -> isModelConfigured
        return validatorService.isModelConfigured(
            p,
            model.value,
            provider.value
        );
    }

    /**
     * 计算属性：是否存在有效 provider
     */
    function hasValidProvider(): boolean {
        return validatorService.hasValidProvider(activeProviders.value);
    }

    // --- Return Store API ---
    return {
        // State
        providerList,
        providerConfigs, // Expose for direct use if needed, though actions are preferred
        model,
        provider,

        // Computed
        activeProviders,
        hasModels,
        allModelList,

        // Getters
        getProvider,
        isModelSelectedByProvider: (
            providerId: string,
            modelName: string
        ): boolean => {
            const p = getProvider(providerId);
            return p?.selectedModels?.includes(modelName) ?? false;
        },
        getProviderSelectedModels: (providerId: string): string[] => {
            const p = getProvider(providerId);
            return p?.selectedModels || [];
        },
        getProviderModels: (providerId: string): string[] => {
            const p = getProvider(providerId);
            return p?.providerModels || [];
        },

        // Actions
        addProvider,
        updateProvider,
        removeProvider,
        setProviderActive,
        fetchProviderModels,
        addModelToProvider,
        removeModelFromProvider,
        deleteModelFromAvailable,
        createOpenAIInstance,
        initialize,

        // Validator
        isApiConfigured,
        isModelConfigured,
        hasValidProvider,
    };
});
