import { UseCase } from "../base/UseCase";
import type { OpenAI } from "openai";
import { summaryService } from "@/services_to_migrate_ddd/summaryService";
import { processContent } from "@/core/utils/messageUtils";

/**
 * 摘要生成用例的输入参数
 */
export interface SummaryInput {
    /** 需要生成摘要的原始文本 */
    originText: string;
    /** OpenAI 实例 */
    openai: OpenAI;
    /** 使用的模型 */
    model: string;
}

/**
 * 完整摘要生成用例的输入参数（包含模型配置）
 */
export interface GenerateSummaryInput {
    /** 需要生成摘要的原始文本 */
    originText: string;
    /** 是否启用单独的总结模型 */
    enableSeparateSummaryModel?: boolean;
    /** 总结模型名称 */
    summaryModel?: string;
    /** 总结模型供应商 */
    summaryProvider?: string;
    /** 模型配置检查函数 */
    isModelConfigured: () => { isValid: boolean };
    /** 创建 OpenAI 实例函数 */
    createOpenAIInstance: (provider?: string) => Promise<any>;
    /** 当前默认模型 */
    defaultModel: string;
}

/**
 * 摘要生成用例的输出结果
 */
export interface SummaryOutput {
    /** 生成的摘要文本 */
    summary: string;
    /** 是否成功 */
    success: boolean;
    /** 错误信息（如果有） */
    error?: string;
}

/**
 * 摘要生成用例
 *
 * 负责调用 AI 模型生成文本摘要的业务逻辑
 * 包含模型调用、错误处理、结果验证等完整流程
 */
export class SummaryUseCase extends UseCase<SummaryInput, SummaryOutput> {
    
    /**
     * 执行摘要生成
     */
    async execute(input: SummaryInput): Promise<SummaryOutput> {
        const { originText, openai, model } = input;

        try {
            // 验证输入参数
            if (!originText || !originText.trim()) {
                return {
                    summary: originText,
                    success: false,
                    error: "原始文本不能为空"
                };
            }

            if (!openai) {
                return {
                    summary: originText,
                    success: false,
                    error: "OpenAI 实例未提供"
                };
            }

            if (!model || !model.trim()) {
                return {
                    summary: originText,
                    success: false,
                    error: "模型名称未提供"
                };
            }

            // 调用 summaryService 生成摘要
            const prompt = this.buildSummaryPrompt(originText);
            const rawResult = await summaryService.callSummaryAPI(openai, model, prompt);

            // 处理结果：去除<think></think>标签并截取前20个字
            const processed = processContent(rawResult || originText);
            const result = processed.mainContent.substring(0, 20);
            
            console.log("已生成摘要:", result);
            
            return {
                summary: result,
                success: true
            };

        } catch (error) {
            console.error("摘要生成失败:", error);
            
            // 根据错误类型提供不同的错误信息
            let errorMessage = "摘要生成失败，请稍后再试";
            
            if (error instanceof Error) {
                if (error.message.includes("rate limit")) {
                    errorMessage = "请求过于频繁，请稍后再试";
                } else if (error.message.includes("invalid_api_key")) {
                    errorMessage = "API密钥无效，请检查配置";
                } else if (error.message.includes("model_not_found")) {
                    errorMessage = "指定的模型不存在";
                }
            }
            
            return {
                summary: originText, // 失败时返回原文本
                success: false,
                error: errorMessage
            };
        }
    }

    /**
     * 构建摘要生成的提示词
     */
    private buildSummaryPrompt(originText: string): string {
        return `任务描述：
请对以下用户提问或回答进行总结，确保总结内容简洁明了。
待总结文本：
{{${originText}}}
要求：
总结长度： 总结内容应简洁明了，不超过20个字。
语言风格： 保持客观、中立，避免主观判断。
示例：
原文本： "我想知道如何提高英语口语能力，尤其是发音和流利度。"
总结： 提高英语口语，发音与流利度。
原文本： "今天天气真好，阳光明媚，适合出去散步。"
总结： 天气好，适合散步。
原文本： "我昨天去了超市，买了牛奶、面包和水果。"
总结： 超市购物，买牛奶面包水果。
原文本： "这个问题比较复杂，需要进一步分析。"
总结： 问题复杂，需进一步分析。
原文本： "你好！"
总结： 你好！`;
    }
}

/**
 * 完整摘要生成用例
 *
 * 包含模型配置检查、OpenAI 实例创建等完整的摘要生成流程
 * 这是从 useSummary composable 迁移过来的业务逻辑
 */
export class GenerateSummaryUseCase extends UseCase<GenerateSummaryInput, SummaryOutput> {

    constructor(private summaryUseCase: SummaryUseCase) {
        super();
    }

    /**
     * 执行完整的摘要生成流程
     */
    async execute(input: GenerateSummaryInput): Promise<SummaryOutput> {
        const {
            originText,
            enableSeparateSummaryModel,
            summaryModel,
            summaryProvider,
            isModelConfigured,
            createOpenAIInstance,
            defaultModel
        } = input;

        try {
            // 验证输入参数
            if (!originText || !originText.trim()) {
                return {
                    summary: originText,
                    success: false,
                    error: "原始文本不能为空"
                };
            }

            // 检查模型配置是否有效
            if (!isModelConfigured().isValid) {
                return {
                    summary: originText,
                    success: false,
                    error: "模型配置不完整，请检查模型、API密钥和基础URL设置"
                };
            }

            let openai;
            let model;

            // 如果启用了单独的总结模型，使用指定的模型和供应商
            if (enableSeparateSummaryModel && summaryModel && summaryProvider) {
                openai = await createOpenAIInstance(summaryProvider);
                model = summaryModel;

                if (!openai) {
                    console.warn("创建总结模型实例失败，回退到当前模型");
                    // 回退到当前模型
                    openai = await createOpenAIInstance();
                    model = defaultModel;
                }
            } else {
                // 使用当前模型进行总结
                openai = await createOpenAIInstance();
                model = defaultModel;
            }

            if (!openai) {
                return {
                    summary: originText,
                    success: false,
                    error: "创建 OpenAI 实例失败，请检查配置"
                };
            }

            // 调用核心摘要生成用例
            return await this.summaryUseCase.execute({
                originText,
                openai,
                model
            });

        } catch (error) {
            console.error("摘要生成失败:", error);

            let errorMessage = "摘要生成失败，请稍后再试";

            if (error instanceof Error) {
                if (error.message.includes("rate limit")) {
                    errorMessage = "请求过于频繁，请稍后再试";
                } else if (error.message.includes("invalid_api_key")) {
                    errorMessage = "API密钥无效，请检查配置";
                } else if (error.message.includes("model_not_found")) {
                    errorMessage = "指定的模型不存在";
                }
            }

            return {
                summary: originText,
                success: false,
                error: errorMessage
            };
        }
    }
}
