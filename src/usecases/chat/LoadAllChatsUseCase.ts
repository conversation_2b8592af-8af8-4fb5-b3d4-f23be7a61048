import { UseCase } from '@/usecases/base/UseCase';
import { Chat } from '@/core/types/chat';
import { chatService } from '@/services_to_migrate_ddd/chatService';

export interface LoadAllChatsInput {
  // 目前不需要输入参数，但保留接口以便将来扩展
}

export interface LoadAllChatsOutput {
  chats: Chat[];
  success: boolean;
}

export class LoadAllChatsUseCase extends UseCase<LoadAllChatsInput, LoadAllChatsOutput> {
  async execute(input: LoadAllChatsInput): Promise<LoadAllChatsOutput> {
    try {
      // 使用现有的 chatService 加载所有聊天
      // 这个功能相对简单，直接使用现有服务
      const chats = await chatService.loadAllChats();

      return {
        chats,
        success: true,
      };
    } catch (error) {
      console.error('Error loading all chats:', error);
      throw error;
    }
  }
}
