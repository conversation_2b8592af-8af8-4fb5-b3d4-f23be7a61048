import { UseCase } from '@/usecases/base/UseCase';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { IMessageRepository } from '@/infrastructure/MessageRepository';
import { Chat, Msg } from '@/core/types/chat';
import { messageService } from '@/services_to_migrate_ddd/messageService';

export interface LoadChatInput {
  chatId: string;
}

export interface LoadChatOutput {
  chat: Chat;
  messages: Msg[];
  success: boolean;
}

export class LoadChatUseCase extends UseCase<LoadChatInput, LoadChatOutput> {
  constructor(
    private chatRepo: IChatRepository,
    private messageRepo: IMessageRepository
  ) {
    super();
  }

  async execute(input: LoadChatInput): Promise<LoadChatOutput> {
    this.validateInput(input);

    try {
      const chat = await this.chatRepo.findById(input.chatId);
      if (!chat) {
        throw new Error(`Chat with id ${input.chatId} not found`);
      }

      // 加载聊天的消息列表
      const messages = await messageService.getChatMessages(input.chatId);

      // 更新聊天对象的消息列表
      chat.msgList = messages;

      return {
        chat,
        messages,
        success: true,
      };
    } catch (error) {
      console.error('Error loading chat:', error);
      throw error;
    }
  }

  private validateInput(input: LoadChatInput) {
    if (!input.chatId) {
      throw new Error('Chat ID is required');
    }
  }
}