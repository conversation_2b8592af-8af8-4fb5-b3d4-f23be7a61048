import { UseCase } from '@/usecases/base/UseCase';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { chatService } from '@/services_to_migrate_ddd/chatService';

export interface RemoveChatInput {
  chatId: string;
}

export interface RemoveChatOutput {
  success: boolean;
}

export class RemoveChatUseCase extends UseCase<RemoveChatInput, RemoveChatOutput> {
  constructor(private chatRepo: IChatRepository) {
    super();
  }

  async execute(input: RemoveChatInput): Promise<RemoveChatOutput> {
    this.validateInput(input);

    try {
      // 使用现有的 chatService 删除聊天
      // 这包括删除聊天记录和相关的消息
      await chatService.removeChat(input.chatId);

      return {
        success: true,
      };
    } catch (error) {
      console.error('Error removing chat:', error);
      throw error;
    }
  }

  private validateInput(input: RemoveChatInput) {
    if (!input.chatId) {
      throw new Error('Chat ID is required');
    }
  }
}
