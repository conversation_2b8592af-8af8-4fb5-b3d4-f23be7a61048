import { UseCase } from '@/usecases/base/UseCase';
import { IChatRepository } from '@/infrastructure/ChatRepository';
import { IMessageRepository } from '@/infrastructure/MessageRepository';
import { Chat, Msg } from '@/core/types/chat';
import { RemoveChatUseCase } from './RemoveChatUseCase';
import { LoadChatUseCase } from './LoadChatUseCase';
import { LoadAllChatsUseCase } from './LoadAllChatsUseCase';

export interface RemoveChatWithActivationInput {
  chatId: string;
  currentActiveChatId: string;
  // 回调函数，用于通知UI层状态变化
  onActiveChatChanged?: (newActiveChatId: string, messages: Msg[]) => void;
  onChatRemoved?: (removedChatId: string) => void;
}

export interface RemoveChatWithActivationOutput {
  success: boolean;
  newActiveChatId: string;
  newActiveMessages: Msg[];
  removedChatId: string;
}

/**
 * 删除会话并处理激活会话切换的用例
 * 
 * 这个用例封装了删除会话后的完整业务流程：
 * 1. 删除指定会话
 * 2. 如果删除的是当前激活会话，自动切换到下一个可用会话
 * 3. 加载新激活会话的消息
 * 4. 通知UI层状态变化
 * 
 * 符合DDD架构原则：
 * - 业务逻辑集中在用例层
 * - UI层只负责调用和展示
 * - 通过组合现有用例实现复杂业务流程
 */
export class RemoveChatWithActivationUseCase extends UseCase<RemoveChatWithActivationInput, RemoveChatWithActivationOutput> {
  constructor(
    private chatRepo: IChatRepository,
    private messageRepo: IMessageRepository,
    private removeChatUseCase: RemoveChatUseCase,
    private loadChatUseCase: LoadChatUseCase,
    private loadAllChatsUseCase: LoadAllChatsUseCase
  ) {
    super();
  }

  async execute(input: RemoveChatWithActivationInput): Promise<RemoveChatWithActivationOutput> {
    this.validateInput(input);

    try {
      // 1. 删除指定会话
      console.log('[RemoveChatWithActivationUseCase] 开始删除会话:', input.chatId);
      await this.removeChatUseCase.execute({ chatId: input.chatId });
      
      // 通知UI层会话已删除
      if (input.onChatRemoved) {
        input.onChatRemoved(input.chatId);
      }

      // 2. 检查是否需要切换激活会话
      if (input.chatId === input.currentActiveChatId) {
        console.log('[RemoveChatWithActivationUseCase] 删除的是当前激活会话，需要切换激活会话');
        
        // 3. 获取剩余的会话列表
        const { chats } = await this.loadAllChatsUseCase.execute({});
        
        if (chats.length > 0) {
          // 4. 选择新的激活会话（选择第一个）
          const newActiveChat = chats[0];
          console.log('[RemoveChatWithActivationUseCase] 切换到新的激活会话:', newActiveChat.id);
          
          // 5. 加载新激活会话的消息
          const { messages } = await this.loadChatUseCase.execute({ chatId: newActiveChat.id });
          console.log('[RemoveChatWithActivationUseCase] 成功加载新激活会话的消息:', messages.length, '条');
          
          // 6. 通知UI层激活会话已变更
          if (input.onActiveChatChanged) {
            input.onActiveChatChanged(newActiveChat.id, messages);
          }
          
          return {
            success: true,
            newActiveChatId: newActiveChat.id,
            newActiveMessages: messages,
            removedChatId: input.chatId,
          };
        } else {
          // 没有其他会话了，设置为默认状态
          console.log('[RemoveChatWithActivationUseCase] 没有其他会话，设置为默认状态');
          
          if (input.onActiveChatChanged) {
            input.onActiveChatChanged("0", []);
          }
          
          return {
            success: true,
            newActiveChatId: "0",
            newActiveMessages: [],
            removedChatId: input.chatId,
          };
        }
      } else {
        // 删除的不是当前激活会话，不需要切换
        console.log('[RemoveChatWithActivationUseCase] 删除的不是当前激活会话，无需切换');
        
        return {
          success: true,
          newActiveChatId: input.currentActiveChatId, // 保持当前激活会话不变
          newActiveMessages: [], // 不需要加载消息
          removedChatId: input.chatId,
        };
      }
    } catch (error) {
      console.error('[RemoveChatWithActivationUseCase] 删除会话失败:', error);
      throw error;
    }
  }

  private validateInput(input: RemoveChatWithActivationInput) {
    if (!input.chatId) {
      throw new Error('Chat ID is required');
    }
    if (!input.currentActiveChatId) {
      throw new Error('Current active chat ID is required');
    }
  }
}
